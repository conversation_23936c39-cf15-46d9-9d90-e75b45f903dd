import { SignatureService } from '../signer';
import { Address } from '@ton/core';

// Mock config for testing
jest.mock('../config', () => ({
  config: {
    signing_private_key: '0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef',
    signing_public_key: 'test_public_key',
    auction_contract_address: 'test_address',
    allowed_origins: ['http://localhost:3000']
  }
}));

describe('SignatureService', () => {
  let signer: SignatureService;

  beforeEach(() => {
    signer = new SignatureService();
  });

  describe('key management', () => {
    it('should return public key as hex string', () => {
      const publicKey = signer.getPublicKey();
      expect(typeof publicKey).toBe('string');
      expect(publicKey.length).toBeGreaterThan(0);
    });

    it('should return public key as BigInt', () => {
      const publicKeyBigInt = signer.getPublicKeyBigInt();
      expect(typeof publicKeyBigInt).toBe('bigint');
      expect(publicKeyBigInt).toBeGreaterThan(0n);
    });

    it('should have consistent public key representations', () => {
      const hexKey = signer.getPublicKey();
      const bigIntKey = signer.getPublicKeyBigInt();
      
      expect(bigIntKey).toBe(BigInt('0x' + hexKey));
    });
  });

  describe('signature generation and verification', () => {
    const mockCalculation = {
      user: Address.parse('EQD4FPq-PRDieyQKkizFTRtSDyucUIqrj0v_zXJmqaDp6_0t'),
      amount: BigInt('100000000000'),
      currency: 0,
      tokens_to_receive: BigInt('1000000000000'),
      current_price: BigInt('100000000'),
      current_round: 1,
      timestamp: Math.floor(Date.now() / 1000),
      nonce: BigInt(1)
    };

    it('should generate signature for purchase calculation', () => {
      const signature = signer.signPurchaseCalculation(mockCalculation);
      
      expect(typeof signature).toBe('string');
      expect(signature.length).toBeGreaterThan(0);
      
      // Should be valid base64
      expect(() => Buffer.from(signature, 'base64')).not.toThrow();
    });

    it('should verify valid signature', () => {
      const signature = signer.signPurchaseCalculation(mockCalculation);
      const isValid = signer.verifySignature(mockCalculation, signature);
      
      expect(isValid).toBe(true);
    });

    it('should reject invalid signature', () => {
      const signature = signer.signPurchaseCalculation(mockCalculation);
      
      // Modify the calculation
      const modifiedCalculation = {
        ...mockCalculation,
        amount: BigInt('200000000000') // Different amount
      };
      
      const isValid = signer.verifySignature(modifiedCalculation, signature);
      expect(isValid).toBe(false);
    });

    it('should reject malformed signature', () => {
      const invalidSignature = 'invalid_signature';
      const isValid = signer.verifySignature(mockCalculation, invalidSignature);
      
      expect(isValid).toBe(false);
    });

    it('should generate different signatures for different calculations', () => {
      const signature1 = signer.signPurchaseCalculation(mockCalculation);
      
      const calculation2 = {
        ...mockCalculation,
        nonce: BigInt(2)
      };
      const signature2 = signer.signPurchaseCalculation(calculation2);
      
      expect(signature1).not.toBe(signature2);
    });

    it('should generate same signature for identical calculations', () => {
      const signature1 = signer.signPurchaseCalculation(mockCalculation);
      const signature2 = signer.signPurchaseCalculation(mockCalculation);
      
      expect(signature1).toBe(signature2);
    });
  });

  describe('edge cases', () => {
    it('should handle zero values correctly', () => {
      const zeroCalculation = {
        user: Address.parse('EQD4FPq-PRDieyQKkizFTRtSDyucUIqrj0v_zXJmqaDp6_0t'),
        amount: BigInt(0),
        currency: 0,
        tokens_to_receive: BigInt(0),
        current_price: BigInt(1),
        current_round: 1,
        timestamp: 0,
        nonce: BigInt(0)
      };

      expect(() => signer.signPurchaseCalculation(zeroCalculation)).not.toThrow();
      
      const signature = signer.signPurchaseCalculation(zeroCalculation);
      const isValid = signer.verifySignature(zeroCalculation, signature);
      expect(isValid).toBe(true);
    });

    it('should handle maximum values correctly', () => {
      const maxCalculation = {
        user: Address.parse('EQD4FPq-PRDieyQKkizFTRtSDyucUIqrj0v_zXJmqaDp6_0t'),
        amount: BigInt('18446744073709551615'), // Max uint64
        currency: 1,
        tokens_to_receive: BigInt('18446744073709551615'),
        current_price: BigInt('18446744073709551615'),
        current_round: 4294967295, // Max uint32
        timestamp: 4294967295,
        nonce: BigInt('18446744073709551615')
      };

      expect(() => signer.signPurchaseCalculation(maxCalculation)).not.toThrow();
      
      const signature = signer.signPurchaseCalculation(maxCalculation);
      const isValid = signer.verifySignature(maxCalculation, signature);
      expect(isValid).toBe(true);
    });

    it('should handle different currencies correctly', () => {
      const tonCalculation = {
        ...mockCalculation,
        currency: 0
      };
      
      const usdtCalculation = {
        ...mockCalculation,
        currency: 1
      };

      const tonSignature = signer.signPurchaseCalculation(tonCalculation);
      const usdtSignature = signer.signPurchaseCalculation(usdtCalculation);

      expect(tonSignature).not.toBe(usdtSignature);
      
      expect(signer.verifySignature(tonCalculation, tonSignature)).toBe(true);
      expect(signer.verifySignature(usdtCalculation, usdtSignature)).toBe(true);
      
      // Cross-verification should fail
      expect(signer.verifySignature(tonCalculation, usdtSignature)).toBe(false);
      expect(signer.verifySignature(usdtCalculation, tonSignature)).toBe(false);
    });
  });

  describe('error handling', () => {
    it('should handle verification errors gracefully', () => {
      // Mock console.error to avoid noise in test output
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      const result = signer.verifySignature(mockCalculation, 'completely_invalid_signature');
      expect(result).toBe(false);
      
      consoleSpy.mockRestore();
    });
  });
});
