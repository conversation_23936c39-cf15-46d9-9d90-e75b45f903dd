import { TokenCalculator } from '../calculator';
import { auctionConfig } from '../config';

describe('TokenCalculator', () => {
  let calculator: TokenCalculator;

  beforeEach(() => {
    calculator = new TokenCalculator();
    calculator.resetNonce();
  });

  describe('calculateCurrentRound', () => {
    it('should calculate round 1 at start time', () => {
      const round = calculator.calculateCurrentRound(auctionConfig.start_time);
      expect(round).toBe(1);
    });

    it('should calculate round 2 after one round duration', () => {
      const round = calculator.calculateCurrentRound(
        auctionConfig.start_time + auctionConfig.round_duration
      );
      expect(round).toBe(2);
    });

    it('should calculate correct round for arbitrary time', () => {
      const elapsed = auctionConfig.round_duration * 5 + 1800; // 5.5 rounds
      const round = calculator.calculateCurrentRound(auctionConfig.start_time + elapsed);
      expect(round).toBe(6); // Should round down to 6
    });
  });

  describe('calculateCurrentPrice', () => {
    it('should return initial price for round 1', () => {
      const price = calculator.calculateCurrentPrice(1);
      expect(price).toBe(auctionConfig.initial_price);
    });

    it('should increase price for round 2', () => {
      const price = calculator.calculateCurrentPrice(2);
      expect(price).toBe(auctionConfig.initial_price + auctionConfig.price_increment);
    });

    it('should calculate correct price for arbitrary round', () => {
      const round = 10;
      const expectedPrice = auctionConfig.initial_price + 
        (auctionConfig.price_increment * BigInt(round - 1));
      const price = calculator.calculateCurrentPrice(round);
      expect(price).toBe(expectedPrice);
    });
  });

  describe('calculateTokensToReceive', () => {
    it('should calculate correct tokens for given amount and price', () => {
      const amount = BigInt('100000000000'); // 100 TON
      const price = BigInt('100000000'); // 0.1 TON per token
      const tokens = calculator.calculateTokensToReceive(amount, price);
      
      // Expected: (100 * 10^9) / 0.1 = 1000 tokens
      expect(tokens).toBe(BigInt('1000000000000')); // 1000 tokens in nanotons
    });

    it('should handle fractional results correctly', () => {
      const amount = BigInt('150000000000'); // 150 TON
      const price = BigInt('110000000'); // 0.11 TON per token
      const tokens = calculator.calculateTokensToReceive(amount, price);
      
      // Expected: (150 * 10^9) / 0.11 ≈ 1363.636... tokens
      const expected = (amount * BigInt('1000000000')) / price;
      expect(tokens).toBe(expected);
    });
  });

  describe('validatePurchase', () => {
    const currentTime = Math.floor(Date.now() / 1000);

    it('should validate correct TON purchase', () => {
      const amount = BigInt('100000000000'); // 100 TON
      const result = calculator.validatePurchase(amount, 0, currentTime);
      expect(result.valid).toBe(true);
    });

    it('should validate correct USDT purchase', () => {
      const amount = BigInt('50000000'); // 50 USDT (6 decimals)
      const result = calculator.validatePurchase(amount, 1, currentTime);
      expect(result.valid).toBe(true);
    });

    it('should reject purchase below minimum for TON', () => {
      const amount = BigInt('10000000000'); // 10 TON (below 50 TON minimum)
      const result = calculator.validatePurchase(amount, 0, currentTime);
      expect(result.valid).toBe(false);
      expect(result.error).toContain('minimum');
    });

    it('should reject purchase below minimum for USDT', () => {
      const amount = BigInt('10000000'); // 10 USDT (below 50 TON equivalent)
      const result = calculator.validatePurchase(amount, 1, currentTime);
      expect(result.valid).toBe(false);
      expect(result.error).toContain('minimum');
    });

    it('should reject purchase before auction start', () => {
      const amount = BigInt('100000000000');
      const earlyTime = auctionConfig.start_time - 3600; // 1 hour before start
      const result = calculator.validatePurchase(amount, 0, earlyTime);
      expect(result.valid).toBe(false);
      expect(result.error).toContain('not started');
    });

    it('should reject purchase after auction end', () => {
      const amount = BigInt('100000000000');
      const lateTime = auctionConfig.end_time + 3600; // 1 hour after end
      const result = calculator.validatePurchase(amount, 0, lateTime);
      expect(result.valid).toBe(false);
      expect(result.error).toContain('ended');
    });
  });

  describe('generatePurchaseCalculation', () => {
    const userAddress = 'EQD4FPq-PRDieyQKkizFTRtSDyucUIqrj0v_zXJmqaDp6_0t';

    it('should generate valid calculation for TON purchase', () => {
      const amount = BigInt('100000000000'); // 100 TON
      const calculation = calculator.generatePurchaseCalculation(userAddress, amount, 0);

      expect(calculation.user.toString()).toBe(userAddress);
      expect(calculation.amount).toBe(amount);
      expect(calculation.currency).toBe(0);
      expect(calculation.tokens_to_receive).toBeGreaterThan(0n);
      expect(calculation.current_price).toBeGreaterThan(0n);
      expect(calculation.current_round).toBeGreaterThan(0);
      expect(calculation.timestamp).toBeGreaterThan(0);
      expect(calculation.nonce).toBeGreaterThan(0n);
    });

    it('should generate valid calculation for USDT purchase', () => {
      const amount = BigInt('100000000'); // 100 USDT
      const calculation = calculator.generatePurchaseCalculation(userAddress, amount, 1);

      expect(calculation.user.toString()).toBe(userAddress);
      expect(calculation.amount).toBe(amount);
      expect(calculation.currency).toBe(1);
      expect(calculation.tokens_to_receive).toBeGreaterThan(0n);
      expect(calculation.current_price).toBeGreaterThan(0n);
      expect(calculation.current_round).toBeGreaterThan(0);
      expect(calculation.timestamp).toBeGreaterThan(0);
      expect(calculation.nonce).toBeGreaterThan(0n);
    });

    it('should generate unique nonces for multiple calculations', () => {
      const amount = BigInt('100000000000');
      const calc1 = calculator.generatePurchaseCalculation(userAddress, amount, 0);
      const calc2 = calculator.generatePurchaseCalculation(userAddress, amount, 0);

      expect(calc1.nonce).not.toBe(calc2.nonce);
      expect(calc2.nonce).toBe(calc1.nonce + 1n);
    });

    it('should throw error for invalid amount', () => {
      const amount = BigInt('1000000000'); // 1 TON (below minimum)
      
      expect(() => {
        calculator.generatePurchaseCalculation(userAddress, amount, 0);
      }).toThrow();
    });

    it('should throw error for invalid address', () => {
      const amount = BigInt('100000000000');
      const invalidAddress = 'invalid_address';
      
      expect(() => {
        calculator.generatePurchaseCalculation(invalidAddress, amount, 0);
      }).toThrow();
    });
  });

  describe('getCurrentAuctionState', () => {
    it('should return valid auction state', () => {
      const state = calculator.getCurrentAuctionState();

      expect(state.current_round).toBeGreaterThan(0);
      expect(state.current_price).toBeGreaterThan(0n);
      expect(state.total_raised).toBeGreaterThan(0n);
      expect(state.total_tokens_sold).toBeGreaterThan(0n);
      expect(state.auction_status).toBe(1); // Active
      expect(state.purchase_count).toBeGreaterThan(0);
    });
  });

  describe('nonce management', () => {
    it('should reset nonce counter', () => {
      const userAddress = 'EQD4FPq-PRDieyQKkizFTRtSDyucUIqrj0v_zXJmqaDp6_0t';
      const amount = BigInt('100000000000');

      // Generate a calculation to increment nonce
      const calc1 = calculator.generatePurchaseCalculation(userAddress, amount, 0);
      expect(calc1.nonce).toBe(1n);

      // Reset nonce
      calculator.resetNonce();

      // Next calculation should start from 1 again
      const calc2 = calculator.generatePurchaseCalculation(userAddress, amount, 0);
      expect(calc2.nonce).toBe(1n);
    });
  });
});
