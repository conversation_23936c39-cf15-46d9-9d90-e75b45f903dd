{"name": "onion-auction-demo-server", "version": "1.0.0", "description": "Demo server for off-chain token calculation and signature generation", "main": "src/index.ts", "scripts": {"start": "node dist/index.js", "dev": "ts-node src/index.ts", "build": "tsc", "test": "jest", "generate-keys": "ts-node scripts/generate-keys.ts", "format": "prettier --write src/**/*.ts", "lint": "eslint src/**/*.ts"}, "dependencies": {"@ton/core": "^0.58.1", "@ton/crypto": "^3.3.0", "express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/node": "^20.10.0", "typescript": "^5.3.0", "ts-node": "^10.9.1", "jest": "^29.7.0", "@types/jest": "^29.5.8", "ts-jest": "^29.1.1"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src"], "testMatch": ["**/__tests__/**/*.test.ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/__tests__/**"]}}