import { Blockchain, SandboxContract, TreasuryContract } from '@ton/sandbox';
import { Address, toNano, beginCell } from '@ton/core';
import { OnionAuction } from '../build/OnionAuction/OnionAuction_OnionAuction';
import { UserPurchase } from '../build/OnionAuction/OnionAuction_UserPurchase';
import { keyPairFromSeed, sign } from '@ton/crypto';
import '@ton/test-utils';

describe('Signature Verification Integration Tests', () => {
    let blockchain: Blockchain;
    let deployer: SandboxContract<TreasuryContract>;
    let user1: SandboxContract<TreasuryContract>;
    let user2: SandboxContract<TreasuryContract>;
    let onionAuction: SandboxContract<OnionAuction>;
    let keyPair: any;
    let publicKeyBigInt: bigint;

    beforeEach(async () => {
        blockchain = await Blockchain.create();
        deployer = await blockchain.treasury('deployer');
        user1 = await blockchain.treasury('user1');
        user2 = await blockchain.treasury('user2');

        // Generate key pair for signing
        const seed = Buffer.from('integration_test_seed_for_signature_verification', 'utf8');
        keyPair = keyPairFromSeed(seed);
        publicKeyBigInt = BigInt('0x' + keyPair.publicKey.toString('hex'));

        // Deploy auction contract
        const startTime = BigInt(Math.floor(Date.now() / 1000));
        const endTime = startTime + 86400n;
        const softCap = toNano('500000');
        const hardCap = toNano('2000000');
        const totalSupply = toNano('1000000');

        onionAuction = blockchain.openContract(
            await OnionAuction.fromInit(
                deployer.address,
                startTime,
                endTime,
                softCap,
                hardCap,
                totalSupply
            )
        );

        await onionAuction.send(deployer.getSender(), { value: toNano('0.5') }, 'Deploy');

        // Set signing key and start auction
        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.1') },
            { $$type: 'SetSigningKey', public_key: publicKeyBigInt }
        );

        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'StartAuction',
                start_time: startTime,
                end_time: endTime,
                soft_cap: softCap,
                hard_cap: hardCap,
                initial_price: toNano('0.1')
            }
        );
    });

    function createPurchaseCalculationCell(calc: any) {
        return beginCell()
            .storeAddress(calc.user)
            .storeCoins(calc.amount)
            .storeUint(calc.currency, 8)
            .storeCoins(calc.tokens_to_receive)
            .storeCoins(calc.current_price)
            .storeUint(calc.current_round, 32)
            .storeUint(calc.timestamp, 64)
            .storeUint(calc.nonce, 64)
            .endCell();
    }

    function signPurchaseCalculation(calc: any) {
        const cell = createPurchaseCalculationCell(calc);
        const hash = cell.hash();
        return sign(hash, keyPair.secretKey);
    }

    it('should complete full signature verification purchase flow', async () => {
        const currentTime = Math.floor(Date.now() / 1000);
        const amount = toNano('100');
        const currentPrice = toNano('0.1');
        const tokensToReceive = (amount * toNano('1')) / currentPrice;

        // Step 1: Create calculation (simulating off-chain service)
        const calculation = {
            user: user1.address,
            amount: amount,
            currency: 0,
            tokens_to_receive: tokensToReceive,
            current_price: currentPrice,
            current_round: 1,
            timestamp: currentTime,
            nonce: 1n
        };

        // Step 2: Sign calculation (simulating off-chain service)
        const signature = signPurchaseCalculation(calculation);

        // Step 3: Execute purchase with signature
        const purchaseResult = await onionAuction.send(
            user1.getSender(),
            { value: toNano('100.2') },
            {
                $$type: 'PurchaseWithSignature',
                calculation: calculation,
                signature: beginCell().storeBuffer(signature).endCell().beginParse()
            }
        );

        expect(purchaseResult.transactions).toHaveTransaction({
            from: user1.address,
            to: onionAuction.address,
            success: true,
        });

        // Step 4: Verify auction state updated
        const totalTokensSold = await onionAuction.getTotalTokensSold();
        expect(totalTokensSold).toBe(tokensToReceive);

        const totalRaised = await onionAuction.getTotalRaised();
        expect(totalRaised).toBe(amount);

        const purchaseCount = await onionAuction.getPurchaseCount();
        expect(purchaseCount).toBe(1n);

        // Step 5: Verify nonce is marked as used
        const isNonceUsed = await onionAuction.getIsNonceUsed(1n);
        expect(isNonceUsed).toBe(true);

        // Step 6: Verify user purchase contract was created
        const userPurchaseAddress = await onionAuction.getUserPurchaseAddress(user1.address);
        const userPurchase = blockchain.openContract(
            await UserPurchase.fromInit(onionAuction.address, user1.address)
        );

        expect(userPurchase.address).toEqualAddress(userPurchaseAddress);

        // Step 7: Verify purchase record in user contract
        const purchaseDetails = await userPurchase.getPurchaseDetails(1n);
        expect(purchaseDetails).toBeTruthy();
        expect(purchaseDetails!.purchase_method).toBe(1n); // Signature verified
        expect(purchaseDetails!.nonce).toBe(1n);
        expect(purchaseDetails!.amount).toBe(amount);
        expect(purchaseDetails!.tokens).toBe(tokensToReceive);
    });

    it('should handle multiple users with signature verification', async () => {
        const currentTime = Math.floor(Date.now() / 1000);
        const amount1 = toNano('100');
        const amount2 = toNano('200');
        const currentPrice = toNano('0.1');

        // User 1 purchase
        const calculation1 = {
            user: user1.address,
            amount: amount1,
            currency: 0,
            tokens_to_receive: (amount1 * toNano('1')) / currentPrice,
            current_price: currentPrice,
            current_round: 1,
            timestamp: currentTime,
            nonce: 1n
        };

        const signature1 = signPurchaseCalculation(calculation1);

        await onionAuction.send(
            user1.getSender(),
            { value: toNano('100.2') },
            {
                $$type: 'PurchaseWithSignature',
                calculation: calculation1,
                signature: beginCell().storeBuffer(signature1).endCell().beginParse()
            }
        );

        // User 2 purchase
        const calculation2 = {
            user: user2.address,
            amount: amount2,
            currency: 0,
            tokens_to_receive: (amount2 * toNano('1')) / currentPrice,
            current_price: currentPrice,
            current_round: 1,
            timestamp: currentTime,
            nonce: 2n
        };

        const signature2 = signPurchaseCalculation(calculation2);

        await onionAuction.send(
            user2.getSender(),
            { value: toNano('200.2') },
            {
                $$type: 'PurchaseWithSignature',
                calculation: calculation2,
                signature: beginCell().storeBuffer(signature2).endCell().beginParse()
            }
        );

        // Verify both purchases were processed
        const totalRaised = await onionAuction.getTotalRaised();
        expect(totalRaised).toBe(amount1 + amount2);

        const purchaseCount = await onionAuction.getPurchaseCount();
        expect(purchaseCount).toBe(2n);

        // Verify both nonces are used
        expect(await onionAuction.getIsNonceUsed(1n)).toBe(true);
        expect(await onionAuction.getIsNonceUsed(2n)).toBe(true);

        // Verify separate user purchase contracts
        const user1PurchaseAddr = await onionAuction.getUserPurchaseAddress(user1.address);
        const user2PurchaseAddr = await onionAuction.getUserPurchaseAddress(user2.address);
        expect(user1PurchaseAddr).not.toEqualAddress(user2PurchaseAddr);
    });

    it('should handle mixed purchase methods (direct and signature)', async () => {
        // First, make a direct purchase (legacy method)
        const directPurchaseResult = await onionAuction.send(
            user1.getSender(),
            { value: toNano('50.1') },
            {
                $$type: 'Purchase',
                amount: toNano('50'),
                currency: 0n
            }
        );

        expect(directPurchaseResult.transactions).toHaveTransaction({
            from: user1.address,
            to: onionAuction.address,
            success: true,
        });

        // Then, make a signature-verified purchase
        const currentTime = Math.floor(Date.now() / 1000);
        const amount = toNano('100');
        const currentPrice = toNano('0.1');

        const calculation = {
            user: user2.address,
            amount: amount,
            currency: 0,
            tokens_to_receive: (amount * toNano('1')) / currentPrice,
            current_price: currentPrice,
            current_round: 1,
            timestamp: currentTime,
            nonce: 1n
        };

        const signature = signPurchaseCalculation(calculation);

        const signaturePurchaseResult = await onionAuction.send(
            user2.getSender(),
            { value: toNano('100.2') },
            {
                $$type: 'PurchaseWithSignature',
                calculation: calculation,
                signature: beginCell().storeBuffer(signature).endCell().beginParse()
            }
        );

        expect(signaturePurchaseResult.transactions).toHaveTransaction({
            from: user2.address,
            to: onionAuction.address,
            success: true,
        });

        // Verify both purchases were processed
        const totalRaised = await onionAuction.getTotalRaised();
        expect(totalRaised).toBe(toNano('150'));

        const purchaseCount = await onionAuction.getPurchaseCount();
        expect(purchaseCount).toBe(2n);

        // Verify user purchase contracts have different purchase methods
        const user1Purchase = blockchain.openContract(
            await UserPurchase.fromInit(onionAuction.address, user1.address)
        );
        const user2Purchase = blockchain.openContract(
            await UserPurchase.fromInit(onionAuction.address, user2.address)
        );

        const user1Details = await user1Purchase.getPurchaseDetails(1n);
        const user2Details = await user2Purchase.getPurchaseDetails(1n);

        expect(user1Details!.purchase_method).toBe(0n); // Direct
        expect(user2Details!.purchase_method).toBe(1n); // Signature verified
    });

    it('should handle refund for signature-verified purchase', async () => {
        // Make a signature-verified purchase
        const currentTime = Math.floor(Date.now() / 1000);
        const amount = toNano('100');
        const currentPrice = toNano('0.1');

        const calculation = {
            user: user1.address,
            amount: amount,
            currency: 0,
            tokens_to_receive: (amount * toNano('1')) / currentPrice,
            current_price: currentPrice,
            current_round: 1,
            timestamp: currentTime,
            nonce: 1n
        };

        const signature = signPurchaseCalculation(calculation);

        await onionAuction.send(
            user1.getSender(),
            { value: toNano('100.2') },
            {
                $$type: 'PurchaseWithSignature',
                calculation: calculation,
                signature: beginCell().storeBuffer(signature).endCell().beginParse()
            }
        );

        // Request refund through user purchase contract
        const userPurchase = blockchain.openContract(
            await UserPurchase.fromInit(onionAuction.address, user1.address)
        );

        const refundResult = await userPurchase.send(
            user1.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'Refund',
                purchase_id: 1n
            }
        );

        expect(refundResult.transactions).toHaveTransaction({
            from: user1.address,
            to: userPurchase.address,
            success: true,
        });

        // Verify refund was processed
        const isRefunded = await userPurchase.getIsRefunded(1n);
        expect(isRefunded).toBe(true);
    });
});
